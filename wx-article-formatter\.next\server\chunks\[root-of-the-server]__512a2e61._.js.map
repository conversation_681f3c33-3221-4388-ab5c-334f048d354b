{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/wx_community/wx-article-formatter/src/lib/formatters.ts"], "sourcesContent": ["// 微信公众号排版样式配置\nexport interface FormatStyle {\n  id: string;\n  name: string;\n  description: string;\n  styles: {\n    title: string;\n    subtitle: string;\n    paragraph: string;\n    quote: string;\n    list: string;\n    image: string;\n    divider: string;\n    emphasis: string;\n    link: string;\n  };\n}\n\n// 预设的排版样式\nexport const formatStyles: FormatStyle[] = [\n  {\n    id: 'classic',\n    name: '经典商务',\n    description: '适合商务、新闻类文章，简洁专业',\n    styles: {\n      title: 'font-size: 24px; font-weight: bold; color: #333; text-align: center; margin: 20px 0; line-height: 1.4;',\n      subtitle: 'font-size: 18px; font-weight: bold; color: #555; margin: 16px 0 8px 0; line-height: 1.4;',\n      paragraph: 'font-size: 16px; color: #333; line-height: 1.8; margin: 12px 0; text-indent: 2em;',\n      quote: 'font-size: 15px; color: #666; background: #f5f5f5; padding: 15px; margin: 15px 0; border-left: 4px solid #007acc; font-style: italic; line-height: 1.6;',\n      list: 'font-size: 16px; color: #333; line-height: 1.6; margin: 8px 0 8px 20px;',\n      image: 'width: 100%; margin: 15px 0;',\n      divider: 'border: none; height: 1px; background: #e0e0e0; margin: 25px 0;',\n      emphasis: 'font-weight: bold; color: #007acc;',\n      link: 'color: #007acc; text-decoration: none;'\n    }\n  },\n  {\n    id: 'modern',\n    name: '现代时尚',\n    description: '适合时尚、生活类文章，活泼现代',\n    styles: {\n      title: 'font-size: 26px; font-weight: bold; color: #2c3e50; text-align: center; margin: 25px 0; line-height: 1.3; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;',\n      subtitle: 'font-size: 20px; font-weight: bold; color: #34495e; margin: 20px 0 10px 0; line-height: 1.3; border-bottom: 2px solid #3498db; padding-bottom: 5px;',\n      paragraph: 'font-size: 16px; color: #2c3e50; line-height: 1.8; margin: 15px 0; text-indent: 0;',\n      quote: 'font-size: 16px; color: #7f8c8d; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); padding: 20px; margin: 20px 0; border-radius: 10px; border-left: 5px solid #3498db; line-height: 1.7;',\n      list: 'font-size: 16px; color: #2c3e50; line-height: 1.7; margin: 10px 0 10px 25px;',\n      image: 'width: 100%; margin: 20px 0;',\n      divider: 'border: none; height: 2px; background: linear-gradient(90deg, #3498db, #9b59b6); margin: 30px 0; border-radius: 1px;',\n      emphasis: 'font-weight: bold; color: #e74c3c; background: #fff3cd; padding: 2px 6px; border-radius: 4px;',\n      link: 'color: #3498db; text-decoration: none; border-bottom: 1px dotted #3498db;'\n    }\n  },\n  {\n    id: 'minimal',\n    name: '极简清新',\n    description: '适合文艺、清新类文章，简约优雅',\n    styles: {\n      title: 'font-size: 22px; font-weight: 300; color: #444; text-align: center; margin: 30px 0; line-height: 1.5; letter-spacing: 2px;',\n      subtitle: 'font-size: 18px; font-weight: 400; color: #666; margin: 25px 0 15px 0; line-height: 1.4; letter-spacing: 1px;',\n      paragraph: 'font-size: 15px; color: #555; line-height: 2; margin: 18px 0; text-indent: 2em; letter-spacing: 0.5px;',\n      quote: 'font-size: 14px; color: #888; background: #fafafa; padding: 20px; margin: 25px 0; border: none; border-left: 3px solid #ddd; line-height: 1.8; font-style: italic;',\n      list: 'font-size: 15px; color: #555; line-height: 1.8; margin: 12px 0 12px 30px;',\n      image: 'width: 100%; margin: 25px 0;',\n      divider: 'border: none; height: 1px; background: #eee; margin: 40px 0;',\n      emphasis: 'font-weight: 500; color: #333; background: #f9f9f9; padding: 1px 4px;',\n      link: 'color: #666; text-decoration: underline; text-decoration-color: #ccc;'\n    }\n  },\n  {\n    id: 'tech',\n    name: '科技蓝调',\n    description: '适合科技、IT类文章，专业现代',\n    styles: {\n      title: 'font-size: 24px; font-weight: bold; color: #1a1a1a; text-align: center; margin: 20px 0; line-height: 1.4; background: #f0f8ff; padding: 15px; border-radius: 8px; border: 2px solid #4a90e2;',\n      subtitle: 'font-size: 19px; font-weight: bold; color: #2c3e50; margin: 18px 0 10px 0; line-height: 1.4; background: #4a90e2; color: white; padding: 8px 15px; border-radius: 5px;',\n      paragraph: 'font-size: 16px; color: #2c3e50; line-height: 1.7; margin: 14px 0; text-indent: 0; background: #f8f9fa; padding: 10px; border-radius: 5px;',\n      quote: 'font-size: 15px; color: #34495e; background: #ecf0f1; padding: 18px; margin: 18px 0; border-left: 4px solid #4a90e2; line-height: 1.6; font-family: \"Courier New\", monospace;',\n      list: 'font-size: 16px; color: #2c3e50; line-height: 1.6; margin: 10px 0 10px 25px; background: #f8f9fa; padding: 5px 10px; border-radius: 3px;',\n      image: 'width: 100%; margin: 18px 0;',\n      divider: 'border: none; height: 2px; background: linear-gradient(90deg, #4a90e2, #50c8ff); margin: 25px 0;',\n      emphasis: 'font-weight: bold; color: #4a90e2; background: #e8f4fd; padding: 3px 8px; border-radius: 4px; border: 1px solid #4a90e2;',\n      link: 'color: #4a90e2; text-decoration: none; font-weight: 500;'\n    }\n  },\n  {\n    id: 'compatible',\n    name: '兼容模式',\n    description: '高兼容性样式，适合复制到其他编辑器',\n    styles: {\n      title: 'font-size: 24px; font-weight: bold; color: #333; text-align: center; margin: 20px 0; line-height: 1.4; background: #f0f8ff; padding: 15px; border: 3px solid #3498db;',\n      subtitle: 'font-size: 18px; font-weight: bold; color: #555; margin: 16px 0 8px 0; line-height: 1.4; border-bottom: 2px solid #3498db; padding-bottom: 5px;',\n      paragraph: 'font-size: 16px; color: #333; line-height: 1.8; margin: 12px 0; text-indent: 0;',\n      quote: 'font-size: 15px; color: #666; background: #f5f5f5; padding: 15px; margin: 15px 0; border: 2px solid #3498db; line-height: 1.6;',\n      list: 'font-size: 16px; color: #333; line-height: 1.6; margin: 8px 0 8px 20px;',\n      image: 'width: 100%; margin: 15px 0; border: 2px solid #3498db;',\n      divider: 'border: none; height: 2px; background: #3498db; margin: 20px 0;',\n      emphasis: 'font-weight: bold; color: #3498db; background: #f0f8ff; padding: 2px 6px; border: 1px solid #3498db;',\n      link: 'color: #3498db; text-decoration: underline; font-weight: 500;'\n    }\n  }\n];\n\n// 内容类型枚举\nexport enum ContentType {\n  TITLE = 'title',\n  SUBTITLE = 'subtitle',\n  PARAGRAPH = 'paragraph',\n  QUOTE = 'quote',\n  LIST = 'list',\n  IMAGE = 'image',\n  DIVIDER = 'divider',\n  EMPHASIS = 'emphasis',\n  LINK = 'link'\n}\n\n// 解析内容并识别类型\nexport function parseContent(htmlContent: string): Array<{type: ContentType, content: string, level?: number}> {\n  // 检查是否在浏览器环境\n  if (typeof window !== 'undefined') {\n    const parser = new DOMParser();\n    const doc = parser.parseFromString(htmlContent, 'text/html');\n    const elements = doc.body.children;\n    return parseElements(elements);\n  } else {\n    // 服务器端使用jsdom\n    const { JSDOM } = require('jsdom');\n    const dom = new JSDOM(htmlContent);\n    const elements = dom.window.document.body.children;\n    return parseElements(elements);\n  }\n}\n\n// 解析DOM元素的通用函数\nfunction parseElements(elements: HTMLCollection): Array<{type: ContentType, content: string, level?: number}> {\n  const result: Array<{type: ContentType, content: string, level?: number}> = [];\n\n  for (let i = 0; i < elements.length; i++) {\n    const element = elements[i] as Element;\n    const tagName = element.tagName.toLowerCase();\n    const textContent = element.textContent?.trim() || '';\n\n    if (!textContent && tagName !== 'hr' && tagName !== 'img') continue;\n\n    switch (tagName) {\n      case 'h1':\n        result.push({ type: ContentType.TITLE, content: textContent });\n        break;\n      case 'h2':\n      case 'h3':\n      case 'h4':\n      case 'h5':\n      case 'h6':\n        result.push({ type: ContentType.SUBTITLE, content: textContent, level: parseInt(tagName[1]) });\n        break;\n      case 'p':\n        if (textContent.length > 0) {\n          result.push({ type: ContentType.PARAGRAPH, content: textContent });\n        }\n        break;\n      case 'blockquote':\n        result.push({ type: ContentType.QUOTE, content: textContent });\n        break;\n      case 'ul':\n      case 'ol':\n        const listItems = element.querySelectorAll('li');\n        listItems.forEach(li => {\n          const itemText = li.textContent?.trim();\n          if (itemText) {\n            result.push({ type: ContentType.LIST, content: itemText });\n          }\n        });\n        break;\n      case 'img':\n        const src = element.getAttribute('src');\n        if (src) {\n          result.push({ type: ContentType.IMAGE, content: src });\n        }\n        break;\n      case 'hr':\n        result.push({ type: ContentType.DIVIDER, content: '' });\n        break;\n      default:\n        // 处理其他包含文本的元素\n        if (textContent.length > 0) {\n          result.push({ type: ContentType.PARAGRAPH, content: textContent });\n        }\n        break;\n    }\n  }\n\n  return result;\n}\n\n// 应用格式化样式\nexport function applyFormatting(\n  parsedContent: Array<{type: ContentType, content: string, level?: number}>,\n  styleId: string\n): string {\n  const style = formatStyles.find(s => s.id === styleId);\n  if (!style) {\n    throw new Error(`Style ${styleId} not found`);\n  }\n\n  let formattedHtml = '';\n\n  parsedContent.forEach(item => {\n    switch (item.type) {\n      case ContentType.TITLE:\n        formattedHtml += `<h1 style=\"${style.styles.title}\">${item.content}</h1>\\n`;\n        break;\n      case ContentType.SUBTITLE:\n        formattedHtml += `<h${item.level || 2} style=\"${style.styles.subtitle}\">${item.content}</h${item.level || 2}>\\n`;\n        break;\n      case ContentType.PARAGRAPH:\n        formattedHtml += `<p style=\"${style.styles.paragraph}\">${item.content}</p>\\n`;\n        break;\n      case ContentType.QUOTE:\n        formattedHtml += `<blockquote style=\"${style.styles.quote}\">${item.content}</blockquote>\\n`;\n        break;\n      case ContentType.LIST:\n        formattedHtml += `<li style=\"${style.styles.list}\">${item.content}</li>\\n`;\n        break;\n      case ContentType.IMAGE:\n        formattedHtml += `<img src=\"${item.content}\" style=\"${style.styles.image}\" alt=\"文章图片\" />\\n`;\n        break;\n      case ContentType.DIVIDER:\n        formattedHtml += `<hr style=\"${style.styles.divider}\" />\\n`;\n        break;\n      default:\n        formattedHtml += `<p style=\"${style.styles.paragraph}\">${item.content}</p>\\n`;\n        break;\n    }\n  });\n\n  return formattedHtml;\n}\n\n// 一键自动排版\nexport function autoFormat(htmlContent: string, styleId: string = 'classic'): string {\n  const parsedContent = parseContent(htmlContent);\n  return applyFormatting(parsedContent, styleId);\n}\n"], "names": [], "mappings": "AAAA,cAAc;;;;;;;;AAmBP,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,OAAO;YACP,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,OAAO;YACP,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,OAAO;YAC<PERSON>,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,OAAO;YACP,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,OAAO;YACP,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;QACR;IACF;CACD;AAGM,IAAA,AAAK,qCAAA;;;;;;;;;;WAAA;;AAaL,SAAS,aAAa,WAAmB;IAC9C,aAAa;IACb;;SAKO;QACL,cAAc;QACd,MAAM,EAAE,KAAK,EAAE;QACf,MAAM,MAAM,IAAI,MAAM;QACtB,MAAM,WAAW,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ;QAClD,OAAO,cAAc;IACvB;AACF;AAEA,eAAe;AACf,SAAS,cAAc,QAAwB;IAC7C,MAAM,SAAsE,EAAE;IAE9E,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,MAAM,UAAU,QAAQ,CAAC,EAAE;QAC3B,MAAM,UAAU,QAAQ,OAAO,CAAC,WAAW;QAC3C,MAAM,cAAc,QAAQ,WAAW,EAAE,UAAU;QAEnD,IAAI,CAAC,eAAe,YAAY,QAAQ,YAAY,OAAO;QAE3D,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC;oBAAE,IAAI;oBAAqB,SAAS;gBAAY;gBAC5D;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,IAAI,CAAC;oBAAE,IAAI;oBAAwB,SAAS;oBAAa,OAAO,SAAS,OAAO,CAAC,EAAE;gBAAE;gBAC5F;YACF,KAAK;gBACH,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,OAAO,IAAI,CAAC;wBAAE,IAAI;wBAAyB,SAAS;oBAAY;gBAClE;gBACA;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;oBAAE,IAAI;oBAAqB,SAAS;gBAAY;gBAC5D;YACF,KAAK;YACL,KAAK;gBACH,MAAM,YAAY,QAAQ,gBAAgB,CAAC;gBAC3C,UAAU,OAAO,CAAC,CAAA;oBAChB,MAAM,WAAW,GAAG,WAAW,EAAE;oBACjC,IAAI,UAAU;wBACZ,OAAO,IAAI,CAAC;4BAAE,IAAI;4BAAoB,SAAS;wBAAS;oBAC1D;gBACF;gBACA;YACF,KAAK;gBACH,MAAM,MAAM,QAAQ,YAAY,CAAC;gBACjC,IAAI,KAAK;oBACP,OAAO,IAAI,CAAC;wBAAE,IAAI;wBAAqB,SAAS;oBAAI;gBACtD;gBACA;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;oBAAE,IAAI;oBAAuB,SAAS;gBAAG;gBACrD;YACF;gBACE,cAAc;gBACd,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,OAAO,IAAI,CAAC;wBAAE,IAAI;wBAAyB,SAAS;oBAAY;gBAClE;gBACA;QACJ;IACF;IAEA,OAAO;AACT;AAGO,SAAS,gBACd,aAA0E,EAC1E,OAAe;IAEf,MAAM,QAAQ,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC9C,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,QAAQ,UAAU,CAAC;IAC9C;IAEA,IAAI,gBAAgB;IAEpB,cAAc,OAAO,CAAC,CAAA;QACpB,OAAQ,KAAK,IAAI;YACf;gBACE,iBAAiB,CAAC,WAAW,EAAE,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC;gBAC3E;YACF;gBACE,iBAAiB,CAAC,EAAE,EAAE,KAAK,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,IAAI,EAAE,GAAG,CAAC;gBAChH;YACF;gBACE,iBAAiB,CAAC,UAAU,EAAE,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC;gBAC7E;YACF;gBACE,iBAAiB,CAAC,mBAAmB,EAAE,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,eAAe,CAAC;gBAC3F;YACF;gBACE,iBAAiB,CAAC,WAAW,EAAE,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC;gBAC1E;YACF;gBACE,iBAAiB,CAAC,UAAU,EAAE,KAAK,OAAO,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC;gBAC3F;YACF;gBACE,iBAAiB,CAAC,WAAW,EAAE,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC3D;YACF;gBACE,iBAAiB,CAAC,UAAU,EAAE,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC;gBAC7E;QACJ;IACF;IAEA,OAAO;AACT;AAGO,SAAS,WAAW,WAAmB,EAAE,UAAkB,SAAS;IACzE,MAAM,gBAAgB,aAAa;IACnC,OAAO,gBAAgB,eAAe;AACxC", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/wx_community/wx-article-formatter/src/app/api/format/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { autoFormat, formatStyles } from '@/lib/formatters';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { content, styleId = 'classic' } = await request.json();\n    \n    if (!content) {\n      return NextResponse.json({ error: '请提供要格式化的内容' }, { status: 400 });\n    }\n\n    // 验证样式ID是否存在\n    const validStyleIds = formatStyles.map(style => style.id);\n    if (!validStyleIds.includes(styleId)) {\n      return NextResponse.json({ \n        error: `无效的样式ID。可用样式: ${validStyleIds.join(', ')}` \n      }, { status: 400 });\n    }\n\n    // 应用自动排版\n    const formattedContent = autoFormat(content, styleId);\n\n    return NextResponse.json({\n      formattedContent,\n      styleId,\n      styleName: formatStyles.find(s => s.id === styleId)?.name\n    });\n  } catch (error) {\n    console.error('格式化失败:', error);\n    return NextResponse.json(\n      { error: '格式化失败，请检查内容格式' }, \n      { status: 500 }\n    );\n  }\n}\n\n// 获取可用的格式化样式\nexport async function GET() {\n  return NextResponse.json({\n    styles: formatStyles.map(style => ({\n      id: style.id,\n      name: style.name,\n      description: style.description\n    }))\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,UAAU,SAAS,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE3D,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAa,GAAG;gBAAE,QAAQ;YAAI;QAClE;QAEA,aAAa;QACb,MAAM,gBAAgB,0HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAA,QAAS,MAAM,EAAE;QACxD,IAAI,CAAC,cAAc,QAAQ,CAAC,UAAU;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO,CAAC,cAAc,EAAE,cAAc,IAAI,CAAC,OAAO;YACpD,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,SAAS;QACT,MAAM,mBAAmB,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,SAAS;QAE7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA;YACA,WAAW,0HAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU;QACvD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,UAAU;QACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,QAAQ,0HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;gBACjC,IAAI,MAAM,EAAE;gBACZ,MAAM,MAAM,IAAI;gBAChB,aAAa,MAAM,WAAW;YAChC,CAAC;IACH;AACF", "debugId": null}}]}