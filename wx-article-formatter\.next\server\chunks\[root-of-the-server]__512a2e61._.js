module.exports = {

"[project]/.next-internal/server/app/api/format/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/jsdom [external] (jsdom, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("jsdom", () => require("jsdom"));

module.exports = mod;
}}),
"[project]/src/lib/formatters.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// 微信公众号排版样式配置
__turbopack_context__.s({
    "ContentType": ()=>ContentType,
    "applyFormatting": ()=>applyFormatting,
    "autoFormat": ()=>autoFormat,
    "formatStyles": ()=>formatStyles,
    "parseContent": ()=>parseContent
});
const formatStyles = [
    {
        id: 'classic',
        name: '经典商务',
        description: '适合商务、新闻类文章，简洁专业',
        styles: {
            title: 'font-size: 24px; font-weight: bold; color: #333; text-align: center; margin: 20px 0; line-height: 1.4;',
            subtitle: 'font-size: 18px; font-weight: bold; color: #555; margin: 16px 0 8px 0; line-height: 1.4;',
            paragraph: 'font-size: 16px; color: #333; line-height: 1.8; margin: 12px 0; text-indent: 2em;',
            quote: 'font-size: 15px; color: #666; background: #f5f5f5; padding: 15px; margin: 15px 0; border-left: 4px solid #007acc; font-style: italic; line-height: 1.6;',
            list: 'font-size: 16px; color: #333; line-height: 1.6; margin: 8px 0 8px 20px;',
            image: 'width: 100%; margin: 15px 0;',
            divider: 'border: none; height: 1px; background: #e0e0e0; margin: 25px 0;',
            emphasis: 'font-weight: bold; color: #007acc;',
            link: 'color: #007acc; text-decoration: none;'
        }
    },
    {
        id: 'modern',
        name: '现代时尚',
        description: '适合时尚、生活类文章，活泼现代',
        styles: {
            title: 'font-size: 26px; font-weight: bold; color: #2c3e50; text-align: center; margin: 25px 0; line-height: 1.3; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;',
            subtitle: 'font-size: 20px; font-weight: bold; color: #34495e; margin: 20px 0 10px 0; line-height: 1.3; border-bottom: 2px solid #3498db; padding-bottom: 5px;',
            paragraph: 'font-size: 16px; color: #2c3e50; line-height: 1.8; margin: 15px 0; text-indent: 0;',
            quote: 'font-size: 16px; color: #7f8c8d; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); padding: 20px; margin: 20px 0; border-radius: 10px; border-left: 5px solid #3498db; line-height: 1.7;',
            list: 'font-size: 16px; color: #2c3e50; line-height: 1.7; margin: 10px 0 10px 25px;',
            image: 'width: 100%; margin: 20px 0;',
            divider: 'border: none; height: 2px; background: linear-gradient(90deg, #3498db, #9b59b6); margin: 30px 0; border-radius: 1px;',
            emphasis: 'font-weight: bold; color: #e74c3c; background: #fff3cd; padding: 2px 6px; border-radius: 4px;',
            link: 'color: #3498db; text-decoration: none; border-bottom: 1px dotted #3498db;'
        }
    },
    {
        id: 'minimal',
        name: '极简清新',
        description: '适合文艺、清新类文章，简约优雅',
        styles: {
            title: 'font-size: 22px; font-weight: 300; color: #444; text-align: center; margin: 30px 0; line-height: 1.5; letter-spacing: 2px;',
            subtitle: 'font-size: 18px; font-weight: 400; color: #666; margin: 25px 0 15px 0; line-height: 1.4; letter-spacing: 1px;',
            paragraph: 'font-size: 15px; color: #555; line-height: 2; margin: 18px 0; text-indent: 2em; letter-spacing: 0.5px;',
            quote: 'font-size: 14px; color: #888; background: #fafafa; padding: 20px; margin: 25px 0; border: none; border-left: 3px solid #ddd; line-height: 1.8; font-style: italic;',
            list: 'font-size: 15px; color: #555; line-height: 1.8; margin: 12px 0 12px 30px;',
            image: 'width: 100%; margin: 25px 0;',
            divider: 'border: none; height: 1px; background: #eee; margin: 40px 0;',
            emphasis: 'font-weight: 500; color: #333; background: #f9f9f9; padding: 1px 4px;',
            link: 'color: #666; text-decoration: underline; text-decoration-color: #ccc;'
        }
    },
    {
        id: 'tech',
        name: '科技蓝调',
        description: '适合科技、IT类文章，专业现代',
        styles: {
            title: 'font-size: 24px; font-weight: bold; color: #1a1a1a; text-align: center; margin: 20px 0; line-height: 1.4; background: #f0f8ff; padding: 15px; border-radius: 8px; border: 2px solid #4a90e2;',
            subtitle: 'font-size: 19px; font-weight: bold; color: #2c3e50; margin: 18px 0 10px 0; line-height: 1.4; background: #4a90e2; color: white; padding: 8px 15px; border-radius: 5px;',
            paragraph: 'font-size: 16px; color: #2c3e50; line-height: 1.7; margin: 14px 0; text-indent: 0; background: #f8f9fa; padding: 10px; border-radius: 5px;',
            quote: 'font-size: 15px; color: #34495e; background: #ecf0f1; padding: 18px; margin: 18px 0; border-left: 4px solid #4a90e2; line-height: 1.6; font-family: "Courier New", monospace;',
            list: 'font-size: 16px; color: #2c3e50; line-height: 1.6; margin: 10px 0 10px 25px; background: #f8f9fa; padding: 5px 10px; border-radius: 3px;',
            image: 'width: 100%; margin: 18px 0;',
            divider: 'border: none; height: 2px; background: linear-gradient(90deg, #4a90e2, #50c8ff); margin: 25px 0;',
            emphasis: 'font-weight: bold; color: #4a90e2; background: #e8f4fd; padding: 3px 8px; border-radius: 4px; border: 1px solid #4a90e2;',
            link: 'color: #4a90e2; text-decoration: none; font-weight: 500;'
        }
    },
    {
        id: 'compatible',
        name: '兼容模式',
        description: '高兼容性样式，适合复制到其他编辑器',
        styles: {
            title: 'font-size: 24px; font-weight: bold; color: #333; text-align: center; margin: 20px 0; line-height: 1.4; background: #f0f8ff; padding: 15px; border: 3px solid #3498db;',
            subtitle: 'font-size: 18px; font-weight: bold; color: #555; margin: 16px 0 8px 0; line-height: 1.4; border-bottom: 2px solid #3498db; padding-bottom: 5px;',
            paragraph: 'font-size: 16px; color: #333; line-height: 1.8; margin: 12px 0; text-indent: 0;',
            quote: 'font-size: 15px; color: #666; background: #f5f5f5; padding: 15px; margin: 15px 0; border: 2px solid #3498db; line-height: 1.6;',
            list: 'font-size: 16px; color: #333; line-height: 1.6; margin: 8px 0 8px 20px;',
            image: 'width: 100%; margin: 15px 0; border: 2px solid #3498db;',
            divider: 'border: none; height: 2px; background: #3498db; margin: 20px 0;',
            emphasis: 'font-weight: bold; color: #3498db; background: #f0f8ff; padding: 2px 6px; border: 1px solid #3498db;',
            link: 'color: #3498db; text-decoration: underline; font-weight: 500;'
        }
    }
];
var ContentType = /*#__PURE__*/ function(ContentType) {
    ContentType["TITLE"] = "title";
    ContentType["SUBTITLE"] = "subtitle";
    ContentType["PARAGRAPH"] = "paragraph";
    ContentType["QUOTE"] = "quote";
    ContentType["LIST"] = "list";
    ContentType["IMAGE"] = "image";
    ContentType["DIVIDER"] = "divider";
    ContentType["EMPHASIS"] = "emphasis";
    ContentType["LINK"] = "link";
    return ContentType;
}({});
function parseContent(htmlContent) {
    // 检查是否在浏览器环境
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    else {
        // 服务器端使用jsdom
        const { JSDOM } = __turbopack_context__.r("[externals]/jsdom [external] (jsdom, cjs)");
        const dom = new JSDOM(htmlContent);
        const elements = dom.window.document.body.children;
        return parseElements(elements);
    }
}
// 解析DOM元素的通用函数
function parseElements(elements) {
    const result = [];
    for(let i = 0; i < elements.length; i++){
        const element = elements[i];
        const tagName = element.tagName.toLowerCase();
        const textContent = element.textContent?.trim() || '';
        if (!textContent && tagName !== 'hr' && tagName !== 'img') continue;
        switch(tagName){
            case 'h1':
                result.push({
                    type: "title",
                    content: textContent
                });
                break;
            case 'h2':
            case 'h3':
            case 'h4':
            case 'h5':
            case 'h6':
                result.push({
                    type: "subtitle",
                    content: textContent,
                    level: parseInt(tagName[1])
                });
                break;
            case 'p':
                if (textContent.length > 0) {
                    result.push({
                        type: "paragraph",
                        content: textContent
                    });
                }
                break;
            case 'blockquote':
                result.push({
                    type: "quote",
                    content: textContent
                });
                break;
            case 'ul':
            case 'ol':
                const listItems = element.querySelectorAll('li');
                listItems.forEach((li)=>{
                    const itemText = li.textContent?.trim();
                    if (itemText) {
                        result.push({
                            type: "list",
                            content: itemText
                        });
                    }
                });
                break;
            case 'img':
                const src = element.getAttribute('src');
                if (src) {
                    result.push({
                        type: "image",
                        content: src
                    });
                }
                break;
            case 'hr':
                result.push({
                    type: "divider",
                    content: ''
                });
                break;
            default:
                // 处理其他包含文本的元素
                if (textContent.length > 0) {
                    result.push({
                        type: "paragraph",
                        content: textContent
                    });
                }
                break;
        }
    }
    return result;
}
function applyFormatting(parsedContent, styleId) {
    const style = formatStyles.find((s)=>s.id === styleId);
    if (!style) {
        throw new Error(`Style ${styleId} not found`);
    }
    let formattedHtml = '';
    parsedContent.forEach((item)=>{
        switch(item.type){
            case "title":
                formattedHtml += `<h1 style="${style.styles.title}">${item.content}</h1>\n`;
                break;
            case "subtitle":
                formattedHtml += `<h${item.level || 2} style="${style.styles.subtitle}">${item.content}</h${item.level || 2}>\n`;
                break;
            case "paragraph":
                formattedHtml += `<p style="${style.styles.paragraph}">${item.content}</p>\n`;
                break;
            case "quote":
                formattedHtml += `<blockquote style="${style.styles.quote}">${item.content}</blockquote>\n`;
                break;
            case "list":
                formattedHtml += `<li style="${style.styles.list}">${item.content}</li>\n`;
                break;
            case "image":
                formattedHtml += `<img src="${item.content}" style="${style.styles.image}" alt="文章图片" />\n`;
                break;
            case "divider":
                formattedHtml += `<hr style="${style.styles.divider}" />\n`;
                break;
            default:
                formattedHtml += `<p style="${style.styles.paragraph}">${item.content}</p>\n`;
                break;
        }
    });
    return formattedHtml;
}
function autoFormat(htmlContent, styleId = 'classic') {
    const parsedContent = parseContent(htmlContent);
    return applyFormatting(parsedContent, styleId);
}
}),
"[project]/src/app/api/format/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$formatters$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/formatters.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const { content, styleId = 'classic' } = await request.json();
        if (!content) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '请提供要格式化的内容'
            }, {
                status: 400
            });
        }
        // 验证样式ID是否存在
        const validStyleIds = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$formatters$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatStyles"].map((style)=>style.id);
        if (!validStyleIds.includes(styleId)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `无效的样式ID。可用样式: ${validStyleIds.join(', ')}`
            }, {
                status: 400
            });
        }
        // 应用自动排版
        const formattedContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$formatters$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["autoFormat"])(content, styleId);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            formattedContent,
            styleId,
            styleName: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$formatters$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatStyles"].find((s)=>s.id === styleId)?.name
        });
    } catch (error) {
        console.error('格式化失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '格式化失败，请检查内容格式'
        }, {
            status: 500
        });
    }
}
async function GET() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        styles: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$formatters$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatStyles"].map((style)=>({
                id: style.id,
                name: style.name,
                description: style.description
            }))
    });
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__512a2e61._.js.map